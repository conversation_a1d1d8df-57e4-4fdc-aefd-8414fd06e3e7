import React, { useEffect, useState } from "react";
import { useTranslation } from "react-i18next";
import { useNavigate, useLocation } from "react-router-dom";
import DropzoneComponent from "@c/DropzoneComponent";
import RightViewLayout from "@c/RighViewLayout";
import CustomInput from "@c/CustomInput";
import { toast } from "react-toastify";
import { validateForm, handleImage } from "./utils";
import { add, getDetail, edit } from "@s/PictureService";
import Upload_Image from "@/assets/Icons/Upload_Image.svg?react";
function AddPicture() {
  const { t } = useTranslation();
  const navigate = useNavigate();
  const { state } = useLocation();
  const [base64, setBase64] = useState(null);
  const [image, setImage] = useState();
  const [selectPicture, setSelectPicture] = useState({});
  const [direction, setDirection] = useState({});

  const options = [
    {
      id: "0",
      value: t("picture_library.original_image"),
    },
    { id: "1", value: t("picture_library.dithering_image") },
  ];

  const pictureTypeOptions = [
    { id: "0", value: t("picture_library.general_user") },
    { id: "1", value: t("picture_library.company_logo") },
  ];

  const [payload, setPayload] = useState({
    pictureId: "",
    pictureName: "",
    pictureProcessing: "",
    size: "",
    pictureType: "",
  });

  const [error, setError] = useState({
    pictureId: "",
    pictureName: "",
    pictureProcessing: "",
    size: "",
    pictureType: "",
  });

  useEffect(() => {
    if (state?.type == "editor") {
      getDetail(state?.id).then((res) => {
        const updatedPictureType = pictureTypeOptions.find(
          (item) => item.id == res.data.pictureType
        );
        const updatedPictureProcessing = options.find(
          (item) => item.id == res.data.pictureProcessing
        );
        setPayload({
          ...payload,
          ...res.data,
          pictureType: updatedPictureType || null,
          pictureProcessing: updatedPictureProcessing || null,
        });

        setBase64(res?.data.url);
      });
    }
  }, []);

  useEffect(() => {
    setSelectPicture(
      pictureTypeOptions.find((item) => item.id == payload.pictureProcessing)
    );

    setDirection(options.find((item) => item.id == payload.pictureType));
  }, [payload]);

  const handleChange = (event) => {
    const name = event.target.name;
    setPayload({
      ...payload,
      [name]: event.target.value,
    });
    setError({
      ...error,
      pictureId: `${t("")}`,
      pictureName: `${t("")}`,
      pictureProcessing: `${t("")}`,
    });
  };

  const handleSelectChange = (name, value) => {
    setPayload({
      ...payload,
      [name]: value,
    });
    setError({
      ...error,
      [name]: "",
    });
  };

  const handleSubmit = (e) => {
    if (validateForm(payload)) {
      if (state?.type == "editor") {
        edit(
          {
            ...payload,
            multipartFile: image,
            pictureProcessing: payload?.pictureProcessing?.id,
            pictureType: payload?.pictureType?.id,
          },
          state?.id
        ).then((res) => {
          if (res?.code == "00000000") {
            toast.success(res.message);
            navigate("/picture-library");
          } else {
            toast.error(res.message);
          }
        });
      } else {
        add({
          ...payload,
          multipartFile: image,
          pictureProcessing: payload?.pictureProcessing?.id,
          pictureType: payload?.pictureType?.id,
        }).then((res) => {
          if (res?.code == "00000000") {
            toast.success(res.message);
            navigate("/picture-library");
          } else {
            toast.error(res.message);
          }
        });
      }
    }
  };

  return (
    <React.Fragment>
      <RightViewLayout
        navigateBack={"/picture-library"}
        title={
          state?.type == "editor"
            ? t("picture_library.edit_picture")
            : t("picture_library.add_picture")
        }>
        <Grid sx={{ height: "100%", width: "100%" }}>
          <Card elevation={0} sx={{ height: "100%" }}>
            <CardContent>
              <Grid container px={2} spacing={2}>
                <Grid
                  item
                  xs={12}
                  md={4}
                  container
                  display={"flex"}
                  justifyContent={"center"}>
                  <Box
                    letiant="standard"
                    style={{
                      display: "flex",
                      justifyContent: "start",
                      width: "100%",
                    }}>
                    <InputLabel
                      shrink
                      htmlFor="bootstrap-input"
                      style={{ paddingLeft: "0px", color: "#474B4F" }}>
                      {t("product.product_picture")}
                    </InputLabel>
                  </Box>
                  <Grid
                    container
                    style={{
                      border: "2px dashed #36C96D",
                      borderRadius: "5px",
                      backgroundColor: "rgba(54, 201, 109,0.1)",
                      display: "flex",
                      justifyContent: "center",
                      alignItems: "center",
                      flexDirection: "column",
                      height: "220px",
                      width: "100%",
                      cursor: "pointer",
                    }}>
                    <DropzoneComponent
                      getExcelFile={(excelData) =>
                        handleImage(excelData, setImage, setBase64)
                      }>
                      {base64 ? (
                        <img
                          src={base64}
                          alt="Uploaded"
                          style={{ maxWidth: "100%", maxHeight: "200px" }}
                        />
                      ) : (
                        <div
                          style={{
                            display: "flex",
                            flexDirection: "column",
                            alignItems: "center",
                            justifyContent: "center",
                          }}>
                          <Upload_Image />
                          <Typography
                            sx={{
                              fontSize: "12px",
                              textAlign: "center",
                              opacity: "0.8",
                              mt: 1,
                            }}>
                            {t("tips_picture.image_size")}
                          </Typography>
                          <Typography
                            sx={{
                              fontSize: "12px",
                              textAlign: "center",
                              opacity: "0.8",
                            }}>
                            {t("tips_picture.support_size")}
                          </Typography>
                          <Typography
                            sx={{
                              fontSize: "12px",
                              textAlign: "center",
                              opacity: "0.8",
                            }}>
                            {t("tips_picture.resolution_size")}
                          </Typography>
                        </div>
                      )}
                    </DropzoneComponent>
                  </Grid>
                </Grid>
                <Grid item container xs={12} md={8} spacing={1}>
                  <Grid item md={6} xs={12}>
                    <CustomInput
                      id="AddPicture1"
                      label={t("picture_library.id")}
                      required
                      size="small"
                      name="pictureId"
                      handleChange={handleChange}
                      value={payload.pictureId}
                      inputProps={{
                        minLength: 5,
                        maxLength: 10,
                      }}
                      validation="alpha-numeric"
                      error={error.pictureId}
                      resetError={() => setError({ ...error, pictureId: "" })}
                      helperText={error.pictureId}
                      placeholder={t("tips_picture.id")}
                    />
                  </Grid>
                  <Grid item md={6} xs={12}>
                    <CustomInput
                      id="Addpicture2"
                      required
                      label={t("picture_library.name")}
                      size="small"
                      name="pictureName"
                      handleChange={handleChange}
                      value={payload.pictureName}
                      validation="alpha-numeric-ch-th"
                      inputProps={{
                        minLength: 5,
                        maxLength: 20,
                      }}
                      error={error.pictureName}
                      resetError={() => setError({ ...error, pictureName: "" })}
                      helperText={error.pictureName}
                      placeholder={t("tips_picture.name")}
                    />
                  </Grid>
                  <Grid item md={6} xs={12}>
                    <InputLabel
                      shrink
                      htmlFor="bootstrap-input"
                      style={{ paddingLeft: "0px" }}>
                      {t("picture_library.type")}{" "}
                      <span style={{ color: "red" }}>*</span>
                    </InputLabel>
                    <Autocomplete
                      noOptionsText={t("tips.no_options")}
                      options={pictureTypeOptions}
                      value={payload.pictureType}
                      getOptionLabel={(option) => option?.value || ""}
                      isOptionEqualToValue={(option, value) =>
                        option.id ? option.id : ""
                      }
                      onChange={(e, v) => {
                        handleSelectChange("pictureType", v);
                      }}
                      name="pictureType"
                      renderInput={(params) => (
                        <TextField
                          {...params}
                          size="small"
                          error={error?.pictureType}
                          helperText={error?.pictureType}
                          sx={{
                            "& .MuiOutlinedInput-input.MuiInputBase-inputSizeSmall":
                              {
                                fontSize: "13px",
                                padding: "12px",
                              },
                          }}
                        />
                      )}
                    />
                  </Grid>
                  <Grid item md={6} xs={12}>
                    <InputLabel
                      shrink
                      htmlFor="bootstrap-input"
                      style={{ paddingLeft: "0px" }}>
                      {t("picture_library.progressing")}{" "}
                      <span style={{ color: "red" }}>*</span>
                    </InputLabel>
                    <Autocomplete
                      noOptionsText={t("tips.no_options")}
                      options={options}
                      value={payload.pictureProcessing}
                      isOptionEqualToValue={(option, value) =>
                        option.id ? option.id : ""
                      }
                      getOptionLabel={(option) => option?.value || ""}
                      onChange={(e, v) => {
                        handleSelectChange("pictureProcessing", v);
                      }}
                      name="pictureProcessing"
                      renderInput={(params) => (
                        <TextField
                          {...params}
                          name="pictureProcessing"
                          size="small"
                          error={error?.pictureProcessing}
                          helperText={error?.pictureProcessing}
                          sx={{
                            "& .MuiOutlinedInput-input.MuiInputBase-inputSizeSmall":
                              {
                                fontSize: "13px",
                                padding: "12px",
                              },
                          }}
                        />
                      )}
                    />
                  </Grid>
                  <Grid item md={12}>
                    <Box
                      sx={{
                        display: "flex",
                        justifyContent: "flex-end",
                        marginTop: "4px",
                      }}>
                      <Button
                        variant="contained"
                        size="large"
                        className="text-transform-none"
                        onClick={() => navigate("/picture-library")}
                        sx={{ marginRight: "10px" }}>
                        {t("common.cancel")}
                      </Button>
                      <Button
                        variant="contained"
                        size="large"
                        style={{
                          background:
                            "linear-gradient(45deg, #1487CA, #78BC27)",
                        }}
                        className="text-transform-none"
                        onClick={handleSubmit}>
                        {t("common.save")}
                      </Button>
                    </Box>
                  </Grid>
                </Grid>
              </Grid>
            </CardContent>
          </Card>
        </Grid>
      </RightViewLayout>
    </React.Fragment>
  );
}

export default AddPicture;
